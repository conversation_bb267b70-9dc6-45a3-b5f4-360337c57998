import React from "react";
import Image from "next/image";
import Link from "next/link";
import games from "@/data/games";
import { generateSlug } from "@/utils/slug";
import { getRunnerLabel, getRunnerColor } from "@/utils/runner";
import { sortGamesByPriority } from "@/utils/gameSort";

export default function Home() {
  // 按优先级排序游戏
  const sortedGames = sortGamesByPriority(games);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-white dark:bg-gray-800 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            PlayUnb
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-1">
            Free Unblocked Games for Anywhere
          </p>
        </div>
      </header>

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
          {sortedGames.map((game, index) => (
            <Link
              key={index}
              href={`/game/${generateSlug(game.name)}`}
              className="group block bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-200"
            >
              <div className="aspect-video relative overflow-hidden">
                <Image
                  src={game.thumbnail}
                  alt={game.name}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-200"
                  sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, (max-width: 1280px) 25vw, 20vw"
                />
                <div className="absolute top-2 right-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRunnerColor(game.runner)}`}>
                    {getRunnerLabel(game.runner)}
                  </span>
                </div>
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 dark:text-white text-sm group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  {game.name}
                </h3>
              </div>
            </Link>
          ))}
        </div>

        {sortedGames.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400">
              No games available at the moment.
            </p>
          </div>
        )}

      </main>

      <footer className="bg-white dark:bg-gray-800 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600 dark:text-gray-300">
            <p>&copy; 2025 PlayUnb. Free Unblocked Games for Anywhere.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
