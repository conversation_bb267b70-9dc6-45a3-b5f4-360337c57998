import { Game } from '@/types/Game';

/**
 * 获取游戏的优先级，如果没有设置则返回默认值60
 */
export function getGamePriority(game: Game): number {
  return game.priority ?? 60;
}

/**
 * 按优先级对游戏进行排序（高优先级在前）
 */
export function sortGamesByPriority(games: Game[]): Game[] {
  return [...games].sort((a, b) => {
    const priorityA = getGamePriority(a);
    const priorityB = getGamePriority(b);
    
    // 按优先级倒序排序（高优先级在前）
    if (priorityA !== priorityB) {
      return priorityB - priorityA;
    }
    
    // 如果优先级相同，按名称字母顺序排序
    return a.name.localeCompare(b.name);
  });
}

/**
 * 获取高优先级游戏（优先级 > 80）
 */
export function getHighPriorityGames(games: Game[]): Game[] {
  return games.filter(game => getGamePriority(game) > 80);
}

/**
 * 获取普通优先级游戏（优先级 <= 80）
 */
export function getNormalPriorityGames(games: Game[]): Game[] {
  return games.filter(game => getGamePriority(game) <= 80);
}
